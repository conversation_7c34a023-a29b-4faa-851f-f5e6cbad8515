import express from "express";
import { createServer } from "http";
import { WebSocketServer } from "ws";
import { parse } from "url";

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

const userSocketMap = new Map();
const activeRooms = new Map(); // roomId -> { participants: Set, roomData: {} }
const userRooms = new Map(); // userId -> roomId

export const getReceiverSocket = (userId) => {
  return userSocketMap.get(userId);
};

// WebRTC signaling handlers
const handleWebRTCSignaling = (ws, userId, data) => {
  const { type, targetUserId, roomId, offer, answer, candidate, callType } =
    data;

  switch (type) {
    case "call-initiate":
      handleCallInitiate(ws, userId, targetUserId, roomId, callType);
      break;
    case "call-accept":
      handleCallAccept(ws, userId, targetUserId, roomId);
      break;
    case "call-reject":
      handleCallReject(userId, targetUserId, roomId);
      break;
    case "call-end":
      handleCallEnd(userId, roomId);
      break;
    case "webrtc-offer":
      handleWebRTCOffer(userId, targetUserId, offer, roomId);
      break;
    case "webrtc-answer":
      handleWebRTCAnswer(userId, targetUserId, answer, roomId);
      break;
    case "webrtc-ice-candidate":
      handleICECandidate(userId, targetUserId, candidate, roomId);
      break;
    case "screen-share-start":
      handleScreenShareStart(userId, roomId);
      break;
    case "screen-share-stop":
      handleScreenShareStop(userId, roomId);
      break;
  }
};

const handleCallInitiate = (
  ws,
  callerId,
  targetUserId,
  roomId,
  callType = "video"
) => {
  const targetSocket = getReceiverSocket(targetUserId);
  if (targetSocket) {
    // Create room
    if (!activeRooms.has(roomId)) {
      activeRooms.set(roomId, {
        participants: new Set([callerId]),
        roomData: {
          initiator: callerId,
          target: targetUserId,
          status: "calling",
          createdAt: new Date(),
          screenSharer: null,
          callType: callType,
        },
      });
    }

    userRooms.set(callerId, roomId);

    targetSocket.send(
      JSON.stringify({
        type: "incoming-call",
        callerId,
        roomId,
        callerName: "User", // You can get this from user data
        callType: callType,
      })
    );

    ws.send(
      JSON.stringify({
        type: "call-initiated",
        roomId,
        targetUserId,
      })
    );
  } else {
    ws.send(
      JSON.stringify({
        type: "call-failed",
        reason: "User offline",
      })
    );
  }
};

const handleCallAccept = (ws, userId, callerId, roomId) => {
  const room = activeRooms.get(roomId);
  if (room) {
    room.participants.add(userId);
    room.roomData.status = "active";
    userRooms.set(userId, roomId);

    const callerSocket = getReceiverSocket(callerId);
    if (callerSocket) {
      callerSocket.send(
        JSON.stringify({
          type: "call-accepted",
          roomId,
          acceptedBy: userId,
        })
      );
    }

    ws.send(
      JSON.stringify({
        type: "call-joined",
        roomId,
        participants: Array.from(room.participants),
      })
    );
  }
};

const handleCallReject = (userId, callerId, roomId) => {
  const callerSocket = getReceiverSocket(callerId);
  if (callerSocket) {
    callerSocket.send(
      JSON.stringify({
        type: "call-rejected",
        roomId,
        rejectedBy: userId,
      })
    );
  }

  // Clean up room
  activeRooms.delete(roomId);
  userRooms.delete(callerId);
};

const handleCallEnd = (userId, roomId) => {
  const room = activeRooms.get(roomId);
  if (room) {
    // Notify all participants
    room.participants.forEach((participantId) => {
      if (participantId !== userId) {
        const participantSocket = getReceiverSocket(participantId);
        if (participantSocket) {
          participantSocket.send(
            JSON.stringify({
              type: "call-ended",
              roomId,
              endedBy: userId,
            })
          );
        }
      }
      userRooms.delete(participantId);
    });

    activeRooms.delete(roomId);
  }
};

const handleWebRTCOffer = (senderId, targetUserId, offer, roomId) => {
  const targetSocket = getReceiverSocket(targetUserId);
  if (targetSocket) {
    targetSocket.send(
      JSON.stringify({
        type: "webrtc-offer",
        senderId,
        offer,
        roomId,
      })
    );
  }
};

const handleWebRTCAnswer = (senderId, targetUserId, answer, roomId) => {
  const targetSocket = getReceiverSocket(targetUserId);
  if (targetSocket) {
    targetSocket.send(
      JSON.stringify({
        type: "webrtc-answer",
        senderId,
        answer,
        roomId,
      })
    );
  }
};

const handleICECandidate = (senderId, targetUserId, candidate, roomId) => {
  const targetSocket = getReceiverSocket(targetUserId);
  if (targetSocket) {
    targetSocket.send(
      JSON.stringify({
        type: "webrtc-ice-candidate",
        senderId,
        candidate,
        roomId,
      })
    );
  }
};

const handleScreenShareStart = (userId, roomId) => {
  const room = activeRooms.get(roomId);
  if (room) {
    room.roomData.screenSharer = userId;

    // Notify other participants
    room.participants.forEach((participantId) => {
      if (participantId !== userId) {
        const participantSocket = getReceiverSocket(participantId);
        if (participantSocket) {
          participantSocket.send(
            JSON.stringify({
              type: "screen-share-started",
              sharerId: userId,
              roomId,
            })
          );
        }
      }
    });
  }
};

const handleScreenShareStop = (userId, roomId) => {
  const room = activeRooms.get(roomId);
  if (room && room.roomData.screenSharer === userId) {
    room.roomData.screenSharer = null;

    // Notify other participants
    room.participants.forEach((participantId) => {
      if (participantId !== userId) {
        const participantSocket = getReceiverSocket(participantId);
        if (participantSocket) {
          participantSocket.send(
            JSON.stringify({
              type: "screen-share-stopped",
              sharerId: userId,
              roomId,
            })
          );
        }
      }
    });
  }
};

wss.on("connection", (ws, req) => {
  const params = new URLSearchParams(parse(req.url).query || "");
  const userId = params.get("userId");

  if (userId) {
    userSocketMap.set(userId, ws);
  }

  console.log(`User connected: ${userId}`);

  broadcastOnlineUsers();

  ws.on("message", (message) => {
    try {
      const data = JSON.parse(message.toString());

      // Handle different message types
      if (
        (data.type && data.type.startsWith("call-")) ||
        (data.type && data.type.startsWith("webrtc-")) ||
        (data.type && data.type.startsWith("screen-share-"))
      ) {
        handleWebRTCSignaling(ws, userId, data);
      }
      // Handle regular messages (existing functionality)
      else if (data.type === "newMessage") {
        // This would be handled by your existing message system
      }
    } catch (error) {
      console.error("Error parsing WebSocket message:", error);
    }
  });

  ws.on("close", () => {
    console.log(`User disconnected: ${userId}`);

    // Clean up user from any active rooms
    const roomId = userRooms.get(userId);
    if (roomId) {
      handleCallEnd(userId, roomId);
    }

    userSocketMap.delete(userId);
    userRooms.delete(userId);
    broadcastOnlineUsers();
  });
});

const broadcastOnlineUsers = () => {
  const users = [...userSocketMap.keys()];
  userSocketMap.forEach((socket) => {
    socket.send(JSON.stringify({ type: "getOnlineUsers", users }));
  });
};

export { wss, server, app };

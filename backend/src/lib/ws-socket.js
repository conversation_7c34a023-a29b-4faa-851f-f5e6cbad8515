import express from "express";
import { createServer } from "http";
import { WebSocketServer } from "ws";
import { parse } from "url";

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

const userSocketMap = new Map();

export const getReceiverSocket = (userId) => {
  return userSocketMap.get(userId);
};

wss.on("connection", (ws, req) => {
  const params = new URLSearchParams(parse(req.url).query || "");
  const userId = params.get("userId");

  if (userId) {
    userSocketMap.set(userId, ws);
  }

  console.log(`User connected: ${userId}`);

  broadcastOnlineUsers();

  ws.on("close", () => {
    console.log(`User disconnected: ${userId}`);
    userSocketMap.delete(userId);
    broadcastOnlineUsers();
  });
});

const broadcastOnlineUsers = () => {
  const users = [...userSocketMap.keys()];
  userSocketMap.forEach((socket) => {
    socket.send(JSON.stringify({ type: "getOnlineUsers", users }));
  });
};

export { wss, server, app };

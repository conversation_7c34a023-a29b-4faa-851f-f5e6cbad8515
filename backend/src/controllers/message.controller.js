import User from "../models/user.model.js";
import Message from "../models/message.model.js";

import cloudinary from "../lib/cloudinary.js";
import { getReceiverSocket } from "../lib/ws-socket.js";

export const getUsersForSidebar = async (req, res) => {
  try {
    const loggedInUserId = req.user._id;
    const filteredUsers = await User.find({
      _id: { $ne: loggedInUserId },
    }).select("-password");

    res.status(200).json(filteredUsers);
  } catch (error) {
    console.log(`Error in getUsersForSidebar: ${error.message}`);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const getMessages = async (req, res) => {
  try {
    const { id: userToChatId } = req.params;
    const myId = req.user._id;

    const message = await Message.find({
      $or: [
        { senderId: myId, recevierId: userToChatId },
        { senderId: userToChatId, recevierId: myId },
      ],
    });

    res.status(200).json(message);
  } catch (error) {
    console.log(`Error in getMessages controller: ${error.message}`);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

export const sendMessage = async (req, res) => {
  try {
    const { text, image } = req.body;
    const { id: recevierId } = req.params;
    const senderId = req.user._id;

    let imageUrl;
    if (image) {
      //Upload base64 image to cloudinary
      const uploadResponse = await cloudinary.uploader.upload(image, {
        quality: "auto",
        fetch_format: "auto",
      });
      imageUrl = uploadResponse.secure_url;
    }

    const newMessage = new Message({
      senderId,
      recevierId,
      text,
      image: imageUrl,
    });

    await newMessage.save();

    //TODO realtime functionality goes here => Socket.io
    const receiverSocket = getReceiverSocket(recevierId);
    if (receiverSocket) {
      receiverSocket.send(
        JSON.stringify({ type: "newMessage", message: newMessage }),
      );
    }

    res.status(200).json(newMessage);
  } catch (error) {
    console.log(`Error in sendMessage controller: ${error}`);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

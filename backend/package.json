{"name": "backend2", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon ./src/index.js", "start": "node src/index.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "ws": "^8.18.0"}, "devDependencies": {"nodemon": "^3.1.7"}}
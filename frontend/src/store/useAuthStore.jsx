import { create } from "zustand";
import { axiosInstance } from "../lib/axios.jsx";
import toast from "react-hot-toast";

const BASE_URL =
  import.meta.env.MODE === "development" ? "ws://localhost:5001" : "/";

export const useAuthStore = create((set, get) => ({
  authUser: null,
  isSigninUp: false,
  isLoggingUp: false,
  isUpdatingProfile: false,
  isCheckingAuth: true,
  onlineUsers: [],
  socket: null,

  checkAuth: async () => {
    try {
      const res = await axiosInstance.get("/auth/check");

      set({ authUser: res.data });
      get().connectSocket();
    } catch (error) {
      console.log(`Error in checkAuth ${error}`);
      set({ authUser: null });
    } finally {
      set({ isCheckingAuth: false });
    }
  },

  signup: async (data) => {
    set({ isSigninUp: true });
    try {
      const res = await axiosInstance.post("/auth/signup", data);
      set({ authUser: res.data });
      toast.success("Account created successfully");
      get().connectSocket();
    } catch (error) {
      toast.error(error.response.data.message);
    } finally {
      set({ isSigninUp: false });
    }
  },

  login: async (data) => {
    set({ isLoggingUp: true });
    try {
      const res = await axiosInstance.post("/auth/login", data);
      set({ authUser: res.data });

      toast.success("Logged in successfully");
      get().connectSocket();
    } catch (error) {
      toast.error(error.response.data.message);
    } finally {
      set({ isLoggingUp: false });
    }
  },

  logout: async () => {
    try {
      await axiosInstance.post("/auth/logout");
      set({ authUser: null });
      toast.success("Logged out successfully");
      get().disconnectSocket();
    } catch (error) {
      toast.error(error.response.data.message);
    }
  },

  updateProfile: async (data) => {
    set({ isUpdatingProfile: true });
    try {
      const res = await axiosInstance.put("/auth/update-profile", data);
      set({ authUser: res.data });
      toast.success("Profile updated successfully");
    } catch (error) {
      toast.error(error.response.data.message);
    } finally {
      set({ isUpdatingProfile: false });
    }
  },

  connectSocket: () => {
    const { authUser } = get();
    if (!authUser || get().socket) return;

    const socket = new WebSocket(`${BASE_URL}?userId=${authUser._id}`);

    socket.onopen = () => {
      console.log("Connected to WebSocket server");
      set({ socket });
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === "getOnlineUsers") {
        set({ onlineUsers: data.users });
      } else if (data.type === "newMessage") {
        console.log("New message received:", data.message);
      }
    };

    socket.onclose = () => {
      console.log("Disconnected from WebSocket server");
      set({ socket: null });
    };

    socket.onerror = (error) => {
      console.error("WebSocket Error:", error);
      toast.error("WebSocket connection error");
    };
  },

  disconnectSocket: () => {
    if (get().socket) {
      get().socket.close();
      set({ socket: null });
    }
  },
}));

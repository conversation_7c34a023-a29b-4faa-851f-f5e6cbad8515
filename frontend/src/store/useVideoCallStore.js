import { create } from "zustand";
import Peer from "simple-peer";

export const useVideoCallStore = create((set, get) => ({
  // Call state
  isInCall: false,
  isInitiatingCall: false,
  isReceivingCall: false,
  currentRoomId: null,
  callParticipants: [],
  callType: null, // 'video' or 'voice'

  // Streams
  localStream: null,
  remoteStream: null,
  screenStream: null,

  // Peer connection
  peer: null,

  // Media controls
  isVideoEnabled: true,
  isAudioEnabled: true,
  isScreenSharing: false,

  // Call info
  incomingCall: null,
  callError: null,

  // WebSocket reference (will be set from auth store)
  socket: null,

  // Actions
  setSocket: (socket) => set({ socket }),

  // Initialize local media stream
  initializeLocalStream: async (video = true, audio = true) => {
    try {
      // Try to get the requested media first
      let stream;
      try {
        stream = await navigator.mediaDevices.getUserMedia({
          video: video ? { width: 640, height: 480 } : false,
          audio: audio,
        });
      } catch (videoError) {
        console.warn("Failed to get video, trying audio only:", videoError);
        // If video fails, try audio only
        if (audio) {
          try {
            stream = await navigator.mediaDevices.getUserMedia({
              video: false,
              audio: true,
            });
            video = false; // Update video flag since we couldn't get video
          } catch (audioError) {
            console.error("Failed to get any media:", audioError);
            set({ callError: "Failed to access microphone" });
            throw audioError;
          }
        } else {
          throw videoError;
        }
      }

      set({
        localStream: stream,
        isVideoEnabled: video,
        isAudioEnabled: audio,
      });

      return stream;
    } catch (error) {
      console.error("Error accessing media devices:", error);
      set({ callError: "Failed to access camera/microphone" });
      throw error;
    }
  },

  // Start a call
  initiateCall: async (targetUserId, callType = "video") => {
    const { socket, initializeLocalStream } = get();
    if (!socket) return;

    try {
      set({ isInitiatingCall: true, callError: null, callType });

      // Get local stream based on call type
      const isVideo = callType === "video";
      await initializeLocalStream(isVideo, true); // Always get audio

      // Generate room ID
      const roomId = `call_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Send call initiation
      socket.send(
        JSON.stringify({
          type: "call-initiate",
          targetUserId,
          roomId,
          callType,
        })
      );

      set({
        currentRoomId: roomId,
        callParticipants: [targetUserId],
        isInCall: true, // Show calling interface immediately
      });
    } catch (error) {
      console.error("Error initiating call:", error);
      set({
        isInitiatingCall: false,
        callError: "Failed to start call",
      });
    }
  },

  // Accept incoming call
  acceptCall: async () => {
    const { socket, incomingCall, initializeLocalStream } = get();
    if (!socket || !incomingCall) return;

    try {
      // Get local stream based on call type
      const isVideo = incomingCall.callType === "video";
      await initializeLocalStream(isVideo, true); // Always get audio

      // Send acceptance
      socket.send(
        JSON.stringify({
          type: "call-accept",
          targetUserId: incomingCall.callerId,
          roomId: incomingCall.roomId,
        })
      );

      set({
        isReceivingCall: false,
        isInCall: true,
        currentRoomId: incomingCall.roomId,
        callParticipants: [incomingCall.callerId],
        callType: incomingCall.callType,
        incomingCall: null,
      });

      // Initialize peer connection as non-initiator
      get().createPeerConnection(false, incomingCall.callerId);
    } catch (error) {
      console.error("Error accepting call:", error);
      set({ callError: "Failed to accept call" });
    }
  },

  // Reject incoming call
  rejectCall: () => {
    const { socket, incomingCall, localStream } = get();
    if (!socket || !incomingCall) return;

    socket.send(
      JSON.stringify({
        type: "call-reject",
        targetUserId: incomingCall.callerId,
        roomId: incomingCall.roomId,
      })
    );

    // Clean up any local stream that might have been created
    if (localStream) {
      localStream.getTracks().forEach((track) => track.stop());
    }

    set({
      isReceivingCall: false,
      incomingCall: null,
      localStream: null,
      isVideoEnabled: true,
      isAudioEnabled: true,
    });
  },

  // End call
  endCall: () => {
    const {
      socket,
      currentRoomId,
      peer,
      localStream,
      remoteStream,
      screenStream,
    } = get();

    if (socket && currentRoomId) {
      socket.send(
        JSON.stringify({
          type: "call-end",
          roomId: currentRoomId,
        })
      );
    }

    // Clean up streams
    if (localStream) {
      localStream.getTracks().forEach((track) => track.stop());
    }
    if (remoteStream) {
      remoteStream.getTracks().forEach((track) => track.stop());
    }
    if (screenStream) {
      screenStream.getTracks().forEach((track) => track.stop());
    }

    // Clean up peer connection
    if (peer) {
      peer.destroy();
    }

    set({
      isInCall: false,
      isInitiatingCall: false,
      isReceivingCall: false,
      currentRoomId: null,
      callParticipants: [],
      localStream: null,
      remoteStream: null,
      screenStream: null,
      peer: null,
      isVideoEnabled: true,
      isAudioEnabled: true,
      isScreenSharing: false,
      incomingCall: null,
      callError: null,
    });
  },

  // Create peer connection
  createPeerConnection: (initiator, targetUserId) => {
    const { socket, localStream, currentRoomId } = get();
    if (!localStream || !socket) return;

    const peer = new Peer({
      initiator,
      trickle: false,
      stream: localStream,
      config: {
        iceServers: [
          { urls: "stun:stun.l.google.com:19302" },
          { urls: "stun:stun1.l.google.com:19302" },
        ],
      },
    });

    peer.on("signal", (data) => {
      if (data.type === "offer") {
        socket.send(
          JSON.stringify({
            type: "webrtc-offer",
            targetUserId,
            offer: data,
            roomId: currentRoomId,
          })
        );
      } else if (data.type === "answer") {
        socket.send(
          JSON.stringify({
            type: "webrtc-answer",
            targetUserId,
            answer: data,
            roomId: currentRoomId,
          })
        );
      }
    });

    peer.on("stream", (stream) => {
      set({ remoteStream: stream });
    });

    peer.on("error", (error) => {
      console.error("Peer connection error:", error);
      set({ callError: "Connection failed" });
    });

    peer.on("close", () => {
      console.log("Peer connection closed");
    });

    set({ peer });
  },

  // Handle WebRTC signaling
  handleWebRTCSignaling: (data) => {
    const { createPeerConnection } = get();

    switch (data.type) {
      case "incoming-call":
        set({
          isReceivingCall: true,
          incomingCall: {
            callerId: data.callerId,
            roomId: data.roomId,
            callerName: data.callerName,
            callType: data.callType || "video",
          },
        });
        break;

      case "call-accepted":
        set({
          isInitiatingCall: false,
          isInCall: true,
          callError: null, // Clear any previous errors
        });
        // Create peer connection as initiator
        createPeerConnection(true, data.acceptedBy);
        break;

      case "call-rejected": {
        set({
          isInitiatingCall: false,
          callError: "Call was rejected",
        });
        // Clean up without sending another end call message
        const { localStream, remoteStream, screenStream, peer } = get();

        // Clean up streams
        if (localStream) {
          localStream.getTracks().forEach((track) => track.stop());
        }
        if (remoteStream) {
          remoteStream.getTracks().forEach((track) => track.stop());
        }
        if (screenStream) {
          screenStream.getTracks().forEach((track) => track.stop());
        }

        // Clean up peer connection
        if (peer) {
          peer.destroy();
        }

        set({
          isInCall: false,
          isInitiatingCall: false,
          isReceivingCall: false,
          currentRoomId: null,
          callParticipants: [],
          localStream: null,
          remoteStream: null,
          screenStream: null,
          peer: null,
          isVideoEnabled: true,
          isAudioEnabled: true,
          isScreenSharing: false,
          incomingCall: null,
        });
        break;
      }

      case "call-ended":
        get().endCall();
        break;

      case "webrtc-offer": {
        const { peer } = get();
        if (peer) {
          peer.signal(data.offer);
        }
        break;
      }

      case "webrtc-answer": {
        const { peer } = get();
        if (peer) {
          peer.signal(data.answer);
        }
        break;
      }

      case "webrtc-ice-candidate": {
        const { peer } = get();
        if (peer) {
          peer.signal(data.candidate);
        }
        break;
      }

      case "screen-share-started":
        // Handle when someone starts screen sharing
        console.log(`User ${data.sharerId} started screen sharing`);
        break;

      case "screen-share-stopped":
        // Handle when someone stops screen sharing
        console.log(`User ${data.sharerId} stopped screen sharing`);
        break;
    }
  },

  // Toggle video
  toggleVideo: () => {
    const { localStream, isVideoEnabled } = get();
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !isVideoEnabled;
        set({ isVideoEnabled: !isVideoEnabled });
      }
    }
  },

  // Toggle audio
  toggleAudio: () => {
    const { localStream, isAudioEnabled } = get();
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !isAudioEnabled;
        set({ isAudioEnabled: !isAudioEnabled });
      }
    }
  },

  // Start screen sharing
  startScreenShare: async () => {
    const { socket, currentRoomId, peer } = get();
    if (!socket || !currentRoomId) return;

    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true,
      });

      set({
        screenStream,
        isScreenSharing: true,
      });

      // Replace video track in peer connection
      if (peer) {
        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = peer._pc
          .getSenders()
          .find((s) => s.track && s.track.kind === "video");

        if (sender) {
          await sender.replaceTrack(videoTrack);
        }
      }

      // Notify server
      socket.send(
        JSON.stringify({
          type: "screen-share-start",
          roomId: currentRoomId,
        })
      );

      // Handle screen share end (when user stops sharing)
      screenStream.getVideoTracks()[0].onended = () => {
        get().stopScreenShare();
      };
    } catch (error) {
      console.error("Error starting screen share:", error);
      set({ callError: "Failed to start screen sharing" });
    }
  },

  // Stop screen sharing
  stopScreenShare: async () => {
    const { socket, currentRoomId, screenStream, localStream, peer } = get();

    if (screenStream) {
      screenStream.getTracks().forEach((track) => track.stop());
    }

    set({
      screenStream: null,
      isScreenSharing: false,
    });

    // Replace back to camera stream
    if (peer && localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      const sender = peer._pc
        .getSenders()
        .find((s) => s.track && s.track.kind === "video");

      if (sender && videoTrack) {
        await sender.replaceTrack(videoTrack);
      }
    }

    // Notify server
    if (socket && currentRoomId) {
      socket.send(
        JSON.stringify({
          type: "screen-share-stop",
          roomId: currentRoomId,
        })
      );
    }
  },
}));

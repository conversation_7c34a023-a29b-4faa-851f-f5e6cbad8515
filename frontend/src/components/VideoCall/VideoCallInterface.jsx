import { useEffect, useRef } from "react";
import { useVideoCallStore } from "../../store/useVideoCallStore";
import {
  PhoneOff,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  MonitorOff,
  Settings,
  Users,
  Loader,
} from "lucide-react";

const VideoCallInterface = () => {
  const {
    isInCall,
    isInitiatingCall,
    localStream,
    remoteStream,
    isVideoEnabled,
    isAudioEnabled,
    isScreenSharing,
    callParticipants,
    callType,
    endCall,
    toggleVideo,
    toggleAudio,
    startScreenShare,
    stopScreenShare,
  } = useVideoCallStore();

  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);

  // Set up video streams
  useEffect(() => {
    if (localVideoRef.current && localStream) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteVideoRef.current && remoteStream) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  if (!isInCall) return null;

  const handleScreenShare = () => {
    if (isScreenSharing) {
      stopScreenShare();
    } else {
      startScreenShare();
    }
  };

  const isVoiceCall = callType === "voice";

  return (
    <div className="fixed inset-0 bg-gray-900 z-50 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-white font-medium">
              {isVoiceCall ? "Voice Call" : "Video Call"}
            </span>
          </div>
          <div className="flex items-center gap-1 text-gray-300">
            <Users size={16} />
            <span className="text-sm">{callParticipants.length + 1}</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
            <Settings size={20} className="text-gray-300" />
          </button>
        </div>
      </div>

      {/* Video/Voice Area */}
      <div className="flex-1 relative bg-gray-900 overflow-hidden">
        {isVoiceCall ? (
          /* Voice Call UI */
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                {isInitiatingCall ? (
                  <Loader size={48} className="text-white animate-spin" />
                ) : (
                  <Users size={48} className="text-white" />
                )}
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">
                {isInitiatingCall ? "Calling..." : "Voice Call Active"}
              </h2>
              <p className="text-gray-400">
                {isInitiatingCall ? "Connecting..." : "Audio only"}
              </p>
              {/* Audio visualizer placeholder */}
              <div className="flex justify-center gap-1 mt-6">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 bg-blue-500 rounded-full animate-pulse"
                    style={{
                      height: `${Math.random() * 20 + 10}px`,
                      animationDelay: `${i * 0.1}s`,
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Video Call UI */
          <div className="w-full h-full relative">
            {remoteStream ? (
              <video
                ref={remoteVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-800">
                <div className="text-center">
                  <div className="w-24 h-24 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    {isInitiatingCall ? (
                      <Loader
                        size={40}
                        className="text-gray-400 animate-spin"
                      />
                    ) : (
                      <Users size={40} className="text-gray-400" />
                    )}
                  </div>
                  <p className="text-gray-400">
                    {isInitiatingCall
                      ? "Calling..."
                      : "Waiting for participant..."}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Local Video (Picture-in-Picture) - Only show for video calls */}
        {!isVoiceCall && (
          <div className="absolute top-4 right-4 w-64 h-48 bg-gray-800 rounded-lg overflow-hidden border-2 border-gray-600 shadow-lg">
            {localStream && isVideoEnabled ? (
              <video
                ref={localVideoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover transform scale-x-[-1]"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-700">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <VideoOff size={24} className="text-gray-400" />
                  </div>
                  <p className="text-gray-400 text-sm">Camera Off</p>
                </div>
              </div>
            )}

            {/* Local video controls overlay */}
            <div className="absolute bottom-2 left-2 right-2 flex justify-center gap-1">
              <div
                className={`px-2 py-1 rounded text-xs ${
                  !isAudioEnabled ? "bg-red-500" : "bg-gray-800 bg-opacity-75"
                } text-white`}
              >
                {isAudioEnabled ? <Mic size={12} /> : <MicOff size={12} />}
              </div>
              <div
                className={`px-2 py-1 rounded text-xs ${
                  !isVideoEnabled ? "bg-red-500" : "bg-gray-800 bg-opacity-75"
                } text-white`}
              >
                {isVideoEnabled ? <Video size={12} /> : <VideoOff size={12} />}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-gray-800 p-6">
        <div className="flex items-center justify-center gap-4">
          {/* Audio Toggle */}
          <button
            onClick={toggleAudio}
            className={`p-4 rounded-full transition-all duration-200 ${
              isAudioEnabled
                ? "bg-gray-700 hover:bg-gray-600 text-white"
                : "bg-red-500 hover:bg-red-600 text-white"
            }`}
            title={isAudioEnabled ? "Mute" : "Unmute"}
          >
            {isAudioEnabled ? <Mic size={24} /> : <MicOff size={24} />}
          </button>

          {/* Video Toggle - Only show for video calls */}
          {!isVoiceCall && (
            <button
              onClick={toggleVideo}
              className={`p-4 rounded-full transition-all duration-200 ${
                isVideoEnabled
                  ? "bg-gray-700 hover:bg-gray-600 text-white"
                  : "bg-red-500 hover:bg-red-600 text-white"
              }`}
              title={isVideoEnabled ? "Turn off camera" : "Turn on camera"}
            >
              {isVideoEnabled ? <Video size={24} /> : <VideoOff size={24} />}
            </button>
          )}

          {/* Screen Share Toggle - Only show for video calls */}
          {!isVoiceCall && (
            <button
              onClick={handleScreenShare}
              className={`p-4 rounded-full transition-all duration-200 ${
                isScreenSharing
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-gray-700 hover:bg-gray-600 text-white"
              }`}
              title={isScreenSharing ? "Stop sharing" : "Share screen"}
            >
              {isScreenSharing ? (
                <MonitorOff size={24} />
              ) : (
                <Monitor size={24} />
              )}
            </button>
          )}

          {/* End Call */}
          <button
            onClick={endCall}
            className="p-4 rounded-full bg-red-500 hover:bg-red-600 text-white transition-all duration-200 ml-4"
            title="End call"
          >
            <PhoneOff size={24} />
          </button>
        </div>

        {/* Call Info */}
        <div className="text-center mt-4">
          <p className="text-gray-400 text-sm">
            {isScreenSharing && "You are sharing your screen"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoCallInterface;

import { useEffect, useState } from "react";
import { useVideoCallStore } from "../../store/useVideoCallStore";
import { Phone, PhoneOff, Video } from "lucide-react";

const IncomingCallModal = () => {
  const {
    isReceivingCall,
    incomingCall,
    acceptCall,
    rejectCall
  } = useVideoCallStore();

  const [callDuration, setCallDuration] = useState(0);

  useEffect(() => {
    let interval;
    if (isReceivingCall) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    } else {
      setCallDuration(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isReceivingCall]);

  if (!isReceivingCall || !incomingCall) return null;

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full overflow-hidden animate-pulse-slow">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white text-center">
          <div className="relative">
            <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
              <Video size={32} />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-ping"></div>
          </div>
          <h2 className="text-xl font-bold mb-1">Incoming Video Call</h2>
          <p className="text-blue-100">
            {incomingCall.callerName || 'Unknown User'}
          </p>
        </div>

        {/* Call Info */}
        <div className="p-6 text-center">
          <div className="mb-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-semibold text-lg">
                  {incomingCall.callerName ? incomingCall.callerName.charAt(0).toUpperCase() : 'U'}
                </span>
              </div>
            </div>
            <p className="text-gray-600 text-sm mb-2">
              Calling for {formatDuration(callDuration)}
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Video call</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            {/* Reject Call */}
            <button
              onClick={rejectCall}
              className="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg"
              title="Decline call"
            >
              <PhoneOff size={24} />
            </button>

            {/* Accept Call */}
            <button
              onClick={acceptCall}
              className="w-16 h-16 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center text-white transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg animate-pulse"
              title="Accept call"
            >
              <Phone size={24} />
            </button>
          </div>

          {/* Additional Options */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 mb-3">Quick actions</p>
            <div className="flex gap-2 justify-center">
              <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-700 transition-colors">
                Message
              </button>
              <button className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-700 transition-colors">
                Remind me
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Background Animation */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500 rounded-full opacity-10 animate-ping animation-delay-1000"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-500 rounded-full opacity-10 animate-ping animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-green-500 rounded-full opacity-10 animate-ping animation-delay-3000"></div>
      </div>
    </div>
  );
};

export default IncomingCallModal;

import { useState } from "react";
import { useVideoCallStore } from "../../store/useVideoCallStore";
import { Video, Phone, Loader } from "lucide-react";

const CallControls = ({ targetUserId, targetUserName }) => {
  const {
    isInCall,
    isInitiatingCall,
    initiateCall
  } = useVideoCallStore();

  const [showTooltip, setShowTooltip] = useState(false);

  const handleVideoCall = () => {
    if (!isInCall && !isInitiatingCall) {
      initiateCall(targetUserId);
    }
  };

  const handleVoiceCall = () => {
    // For now, we'll use the same video call but could be extended for audio-only
    if (!isInCall && !isInitiatingCall) {
      initiateCall(targetUserId);
    }
  };

  if (isInCall) return null; // Hide controls when already in call

  return (
    <div className="flex items-center gap-2">
      {/* Voice Call Button */}
      <div className="relative">
        <button
          onClick={handleVoiceCall}
          disabled={isInitiatingCall}
          className="p-2 hover:bg-base-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          onMouseEnter={() => setShowTooltip('voice')}
          onMouseLeave={() => setShowTooltip(false)}
          title={`Voice call ${targetUserName}`}
        >
          {isInitiatingCall ? (
            <Loader size={20} className="animate-spin text-base-content/70" />
          ) : (
            <Phone size={20} className="text-base-content/70 hover:text-base-content" />
          )}
        </button>
        
        {showTooltip === 'voice' && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
            Voice call
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
          </div>
        )}
      </div>

      {/* Video Call Button */}
      <div className="relative">
        <button
          onClick={handleVideoCall}
          disabled={isInitiatingCall}
          className="p-2 hover:bg-base-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          onMouseEnter={() => setShowTooltip('video')}
          onMouseLeave={() => setShowTooltip(false)}
          title={`Video call ${targetUserName}`}
        >
          {isInitiatingCall ? (
            <Loader size={20} className="animate-spin text-base-content/70" />
          ) : (
            <Video size={20} className="text-base-content/70 hover:text-base-content" />
          )}
        </button>
        
        {showTooltip === 'video' && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
            Video call
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
          </div>
        )}
      </div>

      {/* Call Status Indicator */}
      {isInitiatingCall && (
        <div className="flex items-center gap-2 text-sm text-base-content/70">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span>Calling...</span>
        </div>
      )}
    </div>
  );
};

export default CallControls;
